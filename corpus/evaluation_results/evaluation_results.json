{"summary": {"total_files": 4, "average_score": 58.8, "score_distribution": {"excellent": 0, "good": 0, "fair": 1, "poor": 3}, "issue_counts": {"info": 3, "critical": 9, "warning": 4}, "top_issues": [{"issue": "dimension_evaluator_difficulty: 难度分级维度评分较低，需要重点改进", "frequency": 4}, {"issue": "dimension_evaluator_correctness: 正确性维度评分较低，需要重点改进", "frequency": 2}, {"issue": "dimension_evaluator_completeness: 完整性维度评分中等，建议优化", "frequency": 2}, {"issue": "dimension_evaluator_comprehensive: 综合评分: 59.7 - 质量较低，需要全面改进", "frequency": 1}, {"issue": "dimension_evaluator_comprehensive: 综合评分: 59.5 - 质量较低，需要全面改进", "frequency": 1}]}, "reports": [{"corpus_id": "RAN-6808752", "file_path": "corpus/测试用例 RAN-6808752_ UE为免切用户时,lrrm收到DrbAdmitRequest消息发起承载添加流程部分接纳失败校验.md", "overall_score": 57.75, "evaluation_results": [{"evaluator_name": "dimension_evaluator_completeness", "level": "info", "score": 95.78947368421053, "message": "完整性维度评分良好", "details": {"dimension": "completeness", "dimension_name": "完整性", "overall_score": 95.78947368421053, "weighted_score": 364.0, "weight": 1.0, "metric_results": [{"metric_name": "required_fields", "dimension": "completeness", "score": 100.0, "weight": 1.0, "level": "info", "message": "All required fields are present", "details": {"missing_fields": [], "present_fields": 9, "total_fields": 9, "completion_rate": "9/9"}, "suggestions": []}, {"metric_name": "content_structure", "dimension": "completeness", "score": 100, "weight": 1.0, "level": "info", "message": "Content structure is complete", "details": {"structure_issues": [], "issues_count": 0}, "suggestions": []}, {"metric_name": "test_steps_completeness", "dimension": "completeness", "score": 100, "weight": 1.0, "level": "info", "message": "Test steps are complete", "details": {"steps_length": 56, "expected_results_length": 101, "content_ratio": 0.5544554455445545, "issues": []}, "suggestions": []}, {"metric_name": "code_snippets_completeness", "dimension": "completeness", "score": 80, "weight": 0.8, "level": "info", "message": "Code snippets are complete", "details": {"snippets_count": 15, "incomplete_snippets": [{"index": 13, "issues": ["missing or unknown language"]}, {"index": 14, "issues": ["missing or unknown language"]}], "issues": ["Incomplete code snippets: 2"]}, "suggestions": ["Fix snippet 13: missing or unknown language", "Fix snippet 14: missing or unknown language"]}], "enabled_metrics": ["required_fields", "content_structure", "test_steps_completeness", "code_snippets_completeness"], "metric_count": 4}, "suggestions": [], "dimension": "completeness"}, {"evaluator_name": "dimension_evaluator_correctness", "level": "critical", "score": 55.0, "message": "正确性维度评分较低，需要重点改进", "details": {"dimension": "correctness", "dimension_name": "正确性", "overall_score": 55.0, "weighted_score": 165.0, "weight": 1.0, "metric_results": [{"metric_name": "format_correctness", "dimension": "correctness", "score": 80, "weight": 1.0, "level": "info", "message": "Format validation passed", "details": {"format_issues": ["Invalid date format (expected: YYYY-MM-DD)"], "invalid_tags": [], "issues_count": 1}, "suggestions": ["Fix format issue: Invalid date format (expected: YYYY-MM-DD)"]}, {"metric_name": "logical_consistency", "dimension": "correctness", "score": 85, "weight": 1.0, "level": "info", "message": "Content is logically consistent", "details": {"consistency_issues": ["Business and code tags are inconsistent"], "issues_count": 1, "steps_length": 56, "results_length": 101}, "suggestions": ["Fix: Business and code tags are inconsistent"]}, {"metric_name": "data_validity", "dimension": "correctness", "score": 0, "weight": 1.0, "level": "critical", "message": "Critical data validity issues", "details": {"validity_issues": ["Date format is invalid", "Code snippet 14 has unknown language", "Code snippet 15 has unknown language", "Duplicate test steps found: 26"], "duplicate_steps": [12, 17, 20, 21, 24, 25, 27, 28, 30, 35, 36, 37, 39, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 53, 54], "issues_count": 4}, "suggestions": ["Fix: Date format is invalid", "Fix: Code snippet 14 has unknown language", "Fix: Code snippet 15 has unknown language", "Fix: Duplicate test steps found: 26"]}], "enabled_metrics": ["format_correctness", "logical_consistency", "data_validity"], "metric_count": 3}, "suggestions": ["Fix: Date format is invalid", "Fix: Code snippet 14 has unknown language"], "dimension": "correctness"}, {"evaluator_name": "dimension_evaluator_difficulty", "level": "critical", "score": 20.5, "message": "难度分级维度评分较低，需要重点改进", "details": {"dimension": "difficulty", "dimension_name": "难度分级", "overall_score": 20.5, "weighted_score": 57.4, "weight": 0.8, "metric_results": [{"metric_name": "technical_complexity", "dimension": "difficulty", "score": 30, "weight": 1.0, "level": "info", "message": "Low technical complexity", "details": {"difficulty_level": "Low", "complexity_factors": ["Complex code patterns detected"], "keyword_counts": {"high": 0, "medium": 0, "low": 0}, "code_complexity": 30, "steps_complexity": 0}, "suggestions": []}, {"metric_name": "business_complexity", "dimension": "difficulty", "score": 13, "weight": 1.0, "level": "info", "message": "Low business complexity", "details": {"difficulty_level": "Low", "business_factors": [], "domain_matches": {}, "scenario_complexity": 0, "integration_complexity": 13}, "suggestions": []}, {"metric_name": "test_coverage_complexity", "dimension": "difficulty", "score": 18, "weight": 0.8, "level": "warning", "message": "Limited test coverage", "details": {"difficulty_level": "Low", "coverage_factors": ["Test types: negative", "Edge cases: 1"], "test_types": ["negative"], "test_scenarios": 0, "edge_cases": ["失败"], "data_variations": 0}, "suggestions": []}], "enabled_metrics": ["technical_complexity", "business_complexity", "test_coverage_complexity"], "metric_count": 3}, "suggestions": [], "dimension": "difficulty"}, {"evaluator_name": "dimension_evaluator_comprehensive", "level": "critical", "score": 59.71052631578948, "message": "综合评分: 59.7 - 质量较低，需要全面改进", "details": {"overall_score": 59.71052631578948, "dimension_scores": {"completeness": {"score": 95.78947368421053, "weighted_score": 95.78947368421053, "weight": 1.0, "level": "info", "metric_count": 4, "enabled_metrics": ["required_fields", "content_structure", "test_steps_completeness", "code_snippets_completeness"]}, "correctness": {"score": 55.0, "weighted_score": 55.0, "weight": 1.0, "level": "critical", "metric_count": 3, "enabled_metrics": ["format_correctness", "logical_consistency", "data_validity"]}, "difficulty": {"score": 20.5, "weighted_score": 16.400000000000002, "weight": 0.8, "level": "critical", "metric_count": 3, "enabled_metrics": ["technical_complexity", "business_complexity", "test_coverage_complexity"]}}, "weak_dimensions": ["correctness", "difficulty"], "strong_dimensions": ["completeness"], "total_metrics": 10, "evaluation_summary": {"completeness": 95.78947368421053, "correctness": 55.0, "difficulty": 20.5}}, "suggestions": ["重点改进维度: correctness, difficulty", "correctness维度关键问题: Critical data validity issues"]}], "metadata": {"evaluator_count": 1, "total_issues": 3}}, {"corpus_id": "RAN-5946243", "file_path": "corpus/RAN-5946243 RRC建立优化场景MSG5的传递路径：LUCM-_LUC（UlDcchTranmissionMessage）传递远近点标识.md", "overall_score": 57.60969387755102, "evaluation_results": [{"evaluator_name": "dimension_evaluator_completeness", "level": "info", "score": 93.6842105263158, "message": "完整性维度评分良好", "details": {"dimension": "completeness", "dimension_name": "完整性", "overall_score": 93.6842105263158, "weighted_score": 356.0, "weight": 1.0, "metric_results": [{"metric_name": "required_fields", "dimension": "completeness", "score": 100.0, "weight": 1.0, "level": "info", "message": "All required fields are present", "details": {"missing_fields": [], "present_fields": 9, "total_fields": 9, "completion_rate": "9/9"}, "suggestions": []}, {"metric_name": "content_structure", "dimension": "completeness", "score": 100, "weight": 1.0, "level": "info", "message": "Content structure is complete", "details": {"structure_issues": [], "issues_count": 0}, "suggestions": []}, {"metric_name": "test_steps_completeness", "dimension": "completeness", "score": 100, "weight": 1.0, "level": "info", "message": "Test steps are complete", "details": {"steps_length": 69, "expected_results_length": 53, "content_ratio": 1.3018867924528301, "issues": []}, "suggestions": []}, {"metric_name": "code_snippets_completeness", "dimension": "completeness", "score": 70, "weight": 0.8, "level": "warning", "message": "Code snippets need improvement", "details": {"snippets_count": 20, "incomplete_snippets": [{"index": 15, "issues": ["missing or unknown language"]}, {"index": 16, "issues": ["missing or unknown language"]}, {"index": 17, "issues": ["missing or unknown language"]}, {"index": 18, "issues": ["missing or unknown language"]}, {"index": 19, "issues": ["missing or unknown language"]}], "issues": ["Incomplete code snippets: 5"]}, "suggestions": ["Fix snippet 15: missing or unknown language", "Fix snippet 16: missing or unknown language", "Fix snippet 17: missing or unknown language"]}], "enabled_metrics": ["required_fields", "content_structure", "test_steps_completeness", "code_snippets_completeness"], "metric_count": 4}, "suggestions": ["Fix snippet 15: missing or unknown language", "Fix snippet 16: missing or unknown language"], "dimension": "completeness"}, {"evaluator_name": "dimension_evaluator_correctness", "level": "critical", "score": 55.0, "message": "正确性维度评分较低，需要重点改进", "details": {"dimension": "correctness", "dimension_name": "正确性", "overall_score": 55.0, "weighted_score": 165.0, "weight": 1.0, "metric_results": [{"metric_name": "format_correctness", "dimension": "correctness", "score": 80, "weight": 1.0, "level": "info", "message": "Format validation passed", "details": {"format_issues": ["Invalid date format (expected: YYYY-MM-DD)"], "invalid_tags": [], "issues_count": 1}, "suggestions": ["Fix format issue: Invalid date format (expected: YYYY-MM-DD)"]}, {"metric_name": "logical_consistency", "dimension": "correctness", "score": 85, "weight": 1.0, "level": "info", "message": "Content is logically consistent", "details": {"consistency_issues": ["Business and code tags are inconsistent"], "issues_count": 1, "steps_length": 69, "results_length": 53}, "suggestions": ["Fix: Business and code tags are inconsistent"]}, {"metric_name": "data_validity", "dimension": "correctness", "score": 0, "weight": 1.0, "level": "critical", "message": "Critical data validity issues", "details": {"validity_issues": ["Date format is invalid", "Code snippet 16 has unknown language", "Code snippet 17 has unknown language", "Code snippet 18 has unknown language", "Code snippet 19 has unknown language", "Code snippet 20 has unknown language", "Duplicate test steps found: 25"], "duplicate_steps": [8, 25, 28, 29, 32, 33, 34, 36, 37, 39, 40, 43, 46, 48, 49, 50, 52, 53, 55, 56, 57, 58, 59, 61, 62], "issues_count": 7}, "suggestions": ["Fix: Date format is invalid", "Fix: Code snippet 16 has unknown language", "Fix: Code snippet 17 has unknown language", "Fix: Code snippet 18 has unknown language", "Fix: Code snippet 19 has unknown language"]}], "enabled_metrics": ["format_correctness", "logical_consistency", "data_validity"], "metric_count": 3}, "suggestions": ["Fix: Date format is invalid", "Fix: Code snippet 16 has unknown language"], "dimension": "correctness"}, {"evaluator_name": "dimension_evaluator_difficulty", "level": "critical", "score": 22.28571428571429, "message": "难度分级维度评分较低，需要重点改进", "details": {"dimension": "difficulty", "dimension_name": "难度分级", "overall_score": 22.28571428571429, "weighted_score": 62.4, "weight": 0.8, "metric_results": [{"metric_name": "technical_complexity", "dimension": "difficulty", "score": 30, "weight": 1.0, "level": "info", "message": "Low technical complexity", "details": {"difficulty_level": "Low", "complexity_factors": ["Complex code patterns detected"], "keyword_counts": {"high": 0, "medium": 0, "low": 0}, "code_complexity": 30, "steps_complexity": 0}, "suggestions": []}, {"metric_name": "business_complexity", "dimension": "difficulty", "score": 18, "weight": 1.0, "level": "info", "message": "Low business complexity", "details": {"difficulty_level": "Low", "business_factors": [], "domain_matches": {}, "scenario_complexity": 5, "integration_complexity": 13}, "suggestions": []}, {"metric_name": "test_coverage_complexity", "dimension": "difficulty", "score": 18, "weight": 0.8, "level": "warning", "message": "Limited test coverage", "details": {"difficulty_level": "Low", "coverage_factors": ["Test types: negative", "Edge cases: 1"], "test_types": ["negative"], "test_scenarios": 0, "edge_cases": ["错误"], "data_variations": 0}, "suggestions": []}], "enabled_metrics": ["technical_complexity", "business_complexity", "test_coverage_complexity"], "metric_count": 3}, "suggestions": [], "dimension": "difficulty"}, {"evaluator_name": "dimension_evaluator_comprehensive", "level": "critical", "score": 59.468850698174, "message": "综合评分: 59.5 - 质量较低，需要全面改进", "details": {"overall_score": 59.468850698174, "dimension_scores": {"completeness": {"score": 93.6842105263158, "weighted_score": 93.6842105263158, "weight": 1.0, "level": "info", "metric_count": 4, "enabled_metrics": ["required_fields", "content_structure", "test_steps_completeness", "code_snippets_completeness"]}, "correctness": {"score": 55.0, "weighted_score": 55.0, "weight": 1.0, "level": "critical", "metric_count": 3, "enabled_metrics": ["format_correctness", "logical_consistency", "data_validity"]}, "difficulty": {"score": 22.28571428571429, "weighted_score": 17.828571428571433, "weight": 0.8, "level": "critical", "metric_count": 3, "enabled_metrics": ["technical_complexity", "business_complexity", "test_coverage_complexity"]}}, "weak_dimensions": ["correctness", "difficulty"], "strong_dimensions": ["completeness"], "total_metrics": 10, "evaluation_summary": {"completeness": 93.6842105263158, "correctness": 55.0, "difficulty": 22.28571428571429}}, "suggestions": ["重点改进维度: correctness, difficulty", "correctness维度关键问题: Critical data validity issues"]}], "metadata": {"evaluator_count": 1, "total_issues": 3}}, {"corpus_id": "RAN-5869391", "file_path": "corpus/RAN-5869391.md", "overall_score": 63.34693877551021, "evaluation_results": [{"evaluator_name": "dimension_evaluator_completeness", "level": "warning", "score": 71.31578947368422, "message": "完整性维度评分中等，建议优化", "details": {"dimension": "completeness", "dimension_name": "完整性", "overall_score": 71.31578947368422, "weighted_score": 271.0, "weight": 1.0, "metric_results": [{"metric_name": "required_fields", "dimension": "completeness", "score": 100.0, "weight": 1.0, "level": "info", "message": "All required fields are present", "details": {"missing_fields": [], "present_fields": 9, "total_fields": 9, "completion_rate": "9/9"}, "suggestions": []}, {"metric_name": "content_structure", "dimension": "completeness", "score": 75, "weight": 1.0, "level": "warning", "message": "Structure issues found: 1", "details": {"structure_issues": ["Test steps are missing or too short"], "issues_count": 1}, "suggestions": ["Fix: Test steps are missing or too short"]}, {"metric_name": "test_steps_completeness", "dimension": "completeness", "score": 40, "weight": 1.0, "level": "critical", "message": "Test steps are incomplete", "details": {"steps_length": 5, "expected_results_length": 5, "content_ratio": 1.0, "issues": ["Test steps content too short: 5 chars (minimum: 10)", "Expected results content too short: 5 chars (minimum: 10)"]}, "suggestions": ["Expand test steps content (current: 5 chars, minimum: 10)", "Expand expected results content (current: 5 chars, minimum: 10)"]}, {"metric_name": "code_snippets_completeness", "dimension": "completeness", "score": 70, "weight": 0.8, "level": "warning", "message": "Code snippets need improvement", "details": {"snippets_count": 34, "incomplete_snippets": [{"index": 18, "issues": ["content too short"]}, {"index": 23, "issues": ["content too short"]}, {"index": 32, "issues": ["missing or unknown language"]}, {"index": 33, "issues": ["missing or unknown language"]}], "issues": ["Incomplete code snippets: 4"]}, "suggestions": ["Fix snippet 18: content too short", "Fix snippet 23: content too short", "Fix snippet 32: missing or unknown language"]}], "enabled_metrics": ["required_fields", "content_structure", "test_steps_completeness", "code_snippets_completeness"], "metric_count": 4}, "suggestions": ["Fix: Test steps are missing or too short", "Expand test steps content (current: 5 chars, minimum: 10)", "Expand expected results content (current: 5 chars, minimum: 10)", "Fix snippet 18: content too short", "Fix snippet 23: content too short"], "dimension": "completeness"}, {"evaluator_name": "dimension_evaluator_correctness", "level": "info", "score": 80.0, "message": "正确性维度评分良好", "details": {"dimension": "correctness", "dimension_name": "正确性", "overall_score": 80.0, "weighted_score": 240.0, "weight": 1.0, "metric_results": [{"metric_name": "format_correctness", "dimension": "correctness", "score": 75, "weight": 1.0, "level": "warning", "message": "Format issues found: 1", "details": {"format_issues": ["Invalid Gerrit link format"], "invalid_tags": [], "issues_count": 1}, "suggestions": ["Fix format issue: Invalid Gerrit link format"]}, {"metric_name": "logical_consistency", "dimension": "correctness", "score": 85, "weight": 1.0, "level": "info", "message": "Content is logically consistent", "details": {"consistency_issues": ["Business and code tags are inconsistent"], "issues_count": 1, "steps_length": 5, "results_length": 5}, "suggestions": ["Fix: Business and code tags are inconsistent"]}, {"metric_name": "data_validity", "dimension": "correctness", "score": 80, "weight": 1.0, "level": "info", "message": "Data is valid", "details": {"validity_issues": ["TC steps content is too short", "Code snippet 33 has unknown language", "Code snippet 34 has unknown language"], "duplicate_steps": [], "issues_count": 3}, "suggestions": ["Fix: TC steps content is too short", "Fix: Code snippet 33 has unknown language", "Fix: Code snippet 34 has unknown language"]}], "enabled_metrics": ["format_correctness", "logical_consistency", "data_validity"], "metric_count": 3}, "suggestions": ["Fix format issue: Invalid Gerrit link format"], "dimension": "correctness"}, {"evaluator_name": "dimension_evaluator_difficulty", "level": "critical", "score": 37.357142857142854, "message": "难度分级维度评分较低，需要重点改进", "details": {"dimension": "difficulty", "dimension_name": "难度分级", "overall_score": 37.357142857142854, "weighted_score": 104.6, "weight": 0.8, "metric_results": [{"metric_name": "technical_complexity", "dimension": "difficulty", "score": 38, "weight": 1.0, "level": "info", "message": "Low technical complexity", "details": {"difficulty_level": "Low", "complexity_factors": ["Medium complexity keywords: 1", "Complex code patterns detected"], "keyword_counts": {"high": 0, "medium": 1, "low": 0}, "code_complexity": 30, "steps_complexity": 0}, "suggestions": []}, {"metric_name": "business_complexity", "dimension": "difficulty", "score": 37, "weight": 1.0, "level": "info", "message": "Medium business complexity", "details": {"difficulty_level": "Medium", "business_factors": ["Business domains: ['telecom']"], "domain_matches": {"telecom": 3}, "scenario_complexity": 0, "integration_complexity": 13}, "suggestions": []}, {"metric_name": "test_coverage_complexity", "dimension": "difficulty", "score": 37, "weight": 0.8, "level": "warning", "message": "Limited test coverage", "details": {"difficulty_level": "Low", "coverage_factors": ["Test types: negative", "Edge cases: 3"], "test_types": ["negative"], "test_scenarios": 0, "edge_cases": ["超时", "失败", "满"], "data_variations": 3}, "suggestions": []}], "enabled_metrics": ["technical_complexity", "business_complexity", "test_coverage_complexity"], "metric_count": 3}, "suggestions": [], "dimension": "difficulty"}, {"evaluator_name": "dimension_evaluator_comprehensive", "level": "warning", "score": 64.71482277121376, "message": "综合评分: 64.7 - 质量中等，建议优化", "details": {"overall_score": 64.71482277121376, "dimension_scores": {"completeness": {"score": 71.31578947368422, "weighted_score": 71.31578947368422, "weight": 1.0, "level": "warning", "metric_count": 4, "enabled_metrics": ["required_fields", "content_structure", "test_steps_completeness", "code_snippets_completeness"]}, "correctness": {"score": 80.0, "weighted_score": 80.0, "weight": 1.0, "level": "info", "metric_count": 3, "enabled_metrics": ["format_correctness", "logical_consistency", "data_validity"]}, "difficulty": {"score": 37.357142857142854, "weighted_score": 29.885714285714286, "weight": 0.8, "level": "critical", "metric_count": 3, "enabled_metrics": ["technical_complexity", "business_complexity", "test_coverage_complexity"]}}, "weak_dimensions": ["difficulty"], "strong_dimensions": ["correctness"], "total_metrics": 10, "evaluation_summary": {"completeness": 71.31578947368422, "correctness": 80.0, "difficulty": 37.357142857142854}}, "suggestions": ["重点改进维度: difficulty", "completeness维度关键问题: Test steps are incomplete"]}], "metadata": {"evaluator_count": 1, "total_issues": 3}}, {"corpus_id": "RAN-6612580", "file_path": "corpus/测试用例 RAN-6612580_ 低速迁出测量场景，存在gumai惩罚的邻区，异系统测量上报无输出.md", "overall_score": 56.48724489795919, "evaluation_results": [{"evaluator_name": "dimension_evaluator_completeness", "level": "warning", "score": 64.73684210526316, "message": "完整性维度评分中等，建议优化", "details": {"dimension": "completeness", "dimension_name": "完整性", "overall_score": 64.73684210526316, "weighted_score": 246.0, "weight": 1.0, "metric_results": [{"metric_name": "required_fields", "dimension": "completeness", "score": 100.0, "weight": 1.0, "level": "info", "message": "All required fields are present", "details": {"missing_fields": [], "present_fields": 9, "total_fields": 9, "completion_rate": "9/9"}, "suggestions": []}, {"metric_name": "content_structure", "dimension": "completeness", "score": 50, "weight": 1.0, "level": "critical", "message": "Critical structure issues found: 2", "details": {"structure_issues": ["Test steps are missing or too short", "Expected results are missing or too short"], "issues_count": 2}, "suggestions": ["Fix: Test steps are missing or too short", "Fix: Expected results are missing or too short"]}, {"metric_name": "test_steps_completeness", "dimension": "completeness", "score": 40, "weight": 1.0, "level": "critical", "message": "Test steps are incomplete", "details": {"steps_length": 3, "expected_results_length": 3, "content_ratio": 1.0, "issues": ["Test steps content too short: 3 chars (minimum: 10)", "Expected results content too short: 3 chars (minimum: 10)"]}, "suggestions": ["Expand test steps content (current: 3 chars, minimum: 10)", "Expand expected results content (current: 3 chars, minimum: 10)"]}, {"metric_name": "code_snippets_completeness", "dimension": "completeness", "score": 70, "weight": 0.8, "level": "warning", "message": "Code snippets need improvement", "details": {"snippets_count": 28, "incomplete_snippets": [{"index": 16, "issues": ["missing or unknown language"]}, {"index": 17, "issues": ["missing or unknown language"]}, {"index": 18, "issues": ["missing or unknown language"]}, {"index": 19, "issues": ["missing or unknown language"]}, {"index": 20, "issues": ["missing or unknown language"]}, {"index": 21, "issues": ["missing or unknown language"]}, {"index": 22, "issues": ["missing or unknown language"]}, {"index": 23, "issues": ["missing or unknown language"]}, {"index": 24, "issues": ["missing or unknown language"]}, {"index": 25, "issues": ["missing or unknown language", "content too short"]}, {"index": 26, "issues": ["missing or unknown language", "content too short"]}, {"index": 27, "issues": ["missing or unknown language"]}], "issues": ["Incomplete code snippets: 12"]}, "suggestions": ["Fix snippet 16: missing or unknown language", "Fix snippet 17: missing or unknown language", "Fix snippet 18: missing or unknown language"]}], "enabled_metrics": ["required_fields", "content_structure", "test_steps_completeness", "code_snippets_completeness"], "metric_count": 4}, "suggestions": ["Fix: Test steps are missing or too short", "Fix: Expected results are missing or too short", "Expand test steps content (current: 3 chars, minimum: 10)", "Expand expected results content (current: 3 chars, minimum: 10)", "Fix snippet 16: missing or unknown language"], "dimension": "completeness"}, {"evaluator_name": "dimension_evaluator_correctness", "level": "warning", "score": 60.0, "message": "正确性维度评分中等，建议优化", "details": {"dimension": "correctness", "dimension_name": "正确性", "overall_score": 60.0, "weighted_score": 180.0, "weight": 1.0, "metric_results": [{"metric_name": "format_correctness", "dimension": "correctness", "score": 75, "weight": 1.0, "level": "warning", "message": "Format issues found: 1", "details": {"format_issues": ["Invalid Gerrit link format"], "invalid_tags": [], "issues_count": 1}, "suggestions": ["Fix format issue: Invalid Gerrit link format"]}, {"metric_name": "logical_consistency", "dimension": "correctness", "score": 85, "weight": 1.0, "level": "info", "message": "Content is logically consistent", "details": {"consistency_issues": ["Business and code tags are inconsistent"], "issues_count": 1, "steps_length": 3, "results_length": 3}, "suggestions": ["Fix: Business and code tags are inconsistent"]}, {"metric_name": "data_validity", "dimension": "correctness", "score": 20, "weight": 1.0, "level": "critical", "message": "Critical data validity issues", "details": {"validity_issues": ["TC steps content is too short", "TC expected results content is too short", "Code snippet 17 has unknown language", "Code snippet 18 has unknown language", "Code snippet 19 has unknown language", "Code snippet 20 has unknown language", "Code snippet 21 has unknown language", "Code snippet 22 has unknown language", "Code snippet 23 has unknown language", "Code snippet 24 has unknown language", "Code snippet 25 has unknown language", "Code snippet 26 has unknown language", "Code snippet 27 has unknown language", "Code snippet 28 has unknown language"], "duplicate_steps": [], "issues_count": 14}, "suggestions": ["Fix: TC steps content is too short", "Fix: TC expected results content is too short", "Fix: Code snippet 17 has unknown language", "Fix: Code snippet 18 has unknown language", "Fix: Code snippet 19 has unknown language"]}], "enabled_metrics": ["format_correctness", "logical_consistency", "data_validity"], "metric_count": 3}, "suggestions": ["Fix format issue: Invalid Gerrit link format", "Fix: TC steps content is too short", "Fix: TC expected results content is too short"], "dimension": "correctness"}, {"evaluator_name": "dimension_evaluator_difficulty", "level": "critical", "score": 44.07142857142858, "message": "难度分级维度评分较低，需要重点改进", "details": {"dimension": "difficulty", "dimension_name": "难度分级", "overall_score": 44.07142857142858, "weighted_score": 123.4, "weight": 0.8, "metric_results": [{"metric_name": "technical_complexity", "dimension": "difficulty", "score": 30, "weight": 1.0, "level": "info", "message": "Low technical complexity", "details": {"difficulty_level": "Low", "complexity_factors": ["Complex code patterns detected"], "keyword_counts": {"high": 0, "medium": 0, "low": 0}, "code_complexity": 30, "steps_complexity": 0}, "suggestions": []}, {"metric_name": "business_complexity", "dimension": "difficulty", "score": 71, "weight": 1.0, "level": "info", "message": "High business complexity", "details": {"difficulty_level": "High", "business_factors": ["Business domains: ['telecom', 'system']"], "domain_matches": {"telecom": 6, "system": 1}, "scenario_complexity": 5, "integration_complexity": 10}, "suggestions": []}, {"metric_name": "test_coverage_complexity", "dimension": "difficulty", "score": 28, "weight": 0.8, "level": "warning", "message": "Limited test coverage", "details": {"difficulty_level": "Low", "coverage_factors": ["Test types: functional, negative", "Edge cases: 1"], "test_types": ["functional", "negative"], "test_scenarios": 0, "edge_cases": ["失败"], "data_variations": 0}, "suggestions": []}], "enabled_metrics": ["technical_complexity", "business_complexity", "test_coverage_complexity"], "metric_count": 3}, "suggestions": [], "dimension": "difficulty"}, {"evaluator_name": "dimension_evaluator_comprehensive", "level": "critical", "score": 57.14070891514501, "message": "综合评分: 57.1 - 质量较低，需要全面改进", "details": {"overall_score": 57.14070891514501, "dimension_scores": {"completeness": {"score": 64.73684210526316, "weighted_score": 64.73684210526316, "weight": 1.0, "level": "warning", "metric_count": 4, "enabled_metrics": ["required_fields", "content_structure", "test_steps_completeness", "code_snippets_completeness"]}, "correctness": {"score": 60.0, "weighted_score": 60.0, "weight": 1.0, "level": "warning", "metric_count": 3, "enabled_metrics": ["format_correctness", "logical_consistency", "data_validity"]}, "difficulty": {"score": 44.07142857142858, "weighted_score": 35.25714285714286, "weight": 0.8, "level": "critical", "metric_count": 3, "enabled_metrics": ["technical_complexity", "business_complexity", "test_coverage_complexity"]}}, "weak_dimensions": ["difficulty"], "strong_dimensions": [], "total_metrics": 10, "evaluation_summary": {"completeness": 64.73684210526316, "correctness": 60.0, "difficulty": 44.07142857142858}}, "suggestions": ["重点改进维度: difficulty", "completeness维度关键问题: Critical structure issues found: 2", "correctness维度关键问题: Critical data validity issues"]}], "metadata": {"evaluator_count": 1, "total_issues": 4}}], "failed_files": [], "total_processed": 4, "total_failed": 0}